# AITO Reviews Integration Plugin

A WordPress plugin that connects to the AITO SOAP API to fetch and store review scores and totals using WordPress cron scheduling.

## Features

- **SOAP API Integration**: Connects to AITO's review SOAP methods (getFeedbackRatingA, getFeedbackRatingB, etc.)
- **Automated Data Fetching**: Uses WordPress cron to automatically fetch data at configurable intervals
- **Flexible Scheduling**: Multiple cron interval options (15 minutes to daily)
- **Data Storage**: Stores review data in WordPress options for fast retrieval
- **Admin Interface**: Easy-to-use settings page with connection testing
- **Public API**: Functions and shortcodes for displaying review data
- **Error Handling**: Comprehensive error logging and status reporting
- **Responsive Design**: Mobile-friendly display options

## Installation

1. Upload the plugin files to `/wp-content/plugins/aito-reviews/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to Settings > AITO Reviews to configure the plugin

## Configuration

### API Settings

- **API Endpoint URL**: The AITO SOAP API endpoint (default: https://www.aito.com/api/v1/api.asmx)
- **Username**: API authentication username (if required)
- **Password**: API authentication password (if required)
- **Company ID**: Company identifier parameter (if required)
- **API Method**: Choose which AITO review method to use

### Cron Settings

- **Enable Automatic Updates**: Toggle automatic data fetching
- **Update Interval**: Choose how often to fetch data:
  - Every 15 Minutes
  - Every 30 Minutes
  - Hourly
  - Every 2 Hours
  - Every 6 Hours
  - Twice Daily
  - Daily

## AITO Review Methods

The plugin supports multiple AITO SOAP API methods for fetching review data. The available methods can be detected automatically by using the "Test Connection" feature.

### **Common AITO Review Methods:**

#### **getFeedbackRatingA** (Default)
- **Purpose**: Primary method for fetching review scores and totals
- **Usage**: Most commonly used method
- **Returns**: Review score and total count

#### **getFeedbackRatingB**
- **Purpose**: Alternative method with potentially different data structure
- **Usage**: Secondary method, may provide additional data fields
- **Returns**: Review score and total count (possibly with extended data)

#### **getFeedbackRating** (Generic)
- **Purpose**: Generic feedback rating method
- **Usage**: Fallback method if specific A/B methods are not available
- **Returns**: Basic review data

### **Method Selection:**

1. **Automatic Detection**: Use "Test Connection" to see available methods
2. **Manual Selection**: Choose from dropdown in admin settings
3. **Fallback**: Plugin defaults to getFeedbackRatingA if method not specified

### **Method Configuration:**

```php
// Set method programmatically
update_option('aito_reviews_method', 'getFeedbackRatingB');

// Get current method
$current_method = get_option('aito_reviews_method', 'getFeedbackRatingA');
```

## Usage

### PHP Functions

```php
// Get the current review score
$score = aito_get_review_score();

// Get the total number of reviews
$total = aito_get_review_total();

// Get all review data
$data = aito_get_review_data();

// Check if data is available
if (aito_has_review_data()) {
    echo "Score: " . aito_get_review_score_formatted(1);
    echo "Total: " . aito_get_review_total_formatted();
}

// Display formatted data
aito_review_score(1); // Shows score with 1 decimal place
aito_review_total(); // Shows formatted total

// Display complete summary
aito_review_summary(array(
    'show_score' => true,
    'show_total' => true,
    'show_last_update' => true,
    'template' => 'default'
));
```

### Shortcodes

```html
<!-- Display complete summary -->
[aito_reviews]

<!-- Display only the score -->
[aito_reviews show="score" decimals="2"]

<!-- Display only the total -->
[aito_reviews show="total"]

<!-- Display star rating -->
[aito_reviews show="stars"]

<!-- Custom CSS class -->
[aito_reviews class="my-custom-class"]
```

### Template Integration

```php
// In your theme files
if (function_exists('aito_has_review_data') && aito_has_review_data()) {
    echo '<div class="review-display">';
    echo '<span class="score">' . aito_get_review_score_formatted(1) . '</span>';
    echo '<span class="total">(' . aito_get_review_total_formatted() . ' reviews)</span>';
    echo '</div>';
}
```

## Display Options

### Default Summary
```php
aito_review_summary();
```

### Star Rating
```php
aito_review_summary(array('template' => 'stars'));
```

### Custom Styling
```php
aito_review_summary(array(
    'wrapper_class' => 'my-review-wrapper',
    'score_class' => 'my-score-class',
    'total_class' => 'my-total-class'
));
```

## CSS Classes

The plugin provides several CSS classes for styling:

- `.aito-review-summary` - Main wrapper
- `.aito-review-score` - Score display
- `.aito-review-total` - Total reviews display
- `.aito-review-update` - Last update time
- `.aito-star-rating` - Star rating container
- `.aito-star` - Individual star
- `.aito-star.filled` - Filled star
- `.aito-star.half` - Half-filled star
- `.aito-star.empty` - Empty star

## Admin Features

### Settings Page
- Configure API connection details
- Set cron scheduling options
- View current data and status
- Test API connection
- Manually fetch data

### Status Information
- Current review score and total
- Last update timestamp
- Error messages (if any)
- Cron schedule status

### Actions
- **Test Connection**: Verify API connectivity and available methods
- **Fetch Data Now**: Manually trigger data fetch for testing

## Error Handling

The plugin includes comprehensive error handling:

- SOAP connection errors
- API response parsing errors
- Cron scheduling issues
- Data validation errors

All errors are logged and displayed in the admin interface.

## Hooks and Filters

### Actions
- `aito_reviews_data_updated` - Fired when new data is fetched
- `aito_reviews_fetch_error` - Fired when data fetch fails

### Filters
- `aito_reviews_soap_options` - Modify SOAP client options
- `aito_reviews_processed_data` - Filter processed API response data
- `aito_reviews_display_args` - Modify display arguments

## Requirements

### **Critical Requirements**
- **WordPress 5.0+**
- **PHP 7.0+** (7.4+ recommended)
- **PHP SOAP Extension** ⚠️ **REQUIRED** - For SOAP API communication
- **PHP OpenSSL Extension** ⚠️ **REQUIRED** - For HTTPS connections
- **PHP cURL Extension** ⚠️ **REQUIRED** - For HTTP requests
- **Active internet connection** - For API calls to AITO

### **System Check**
The plugin automatically checks these requirements on activation. If any are missing, activation will be prevented with a detailed error message.

📋 **See [REQUIREMENTS.md](REQUIREMENTS.md) for detailed installation instructions and troubleshooting.**

## Troubleshooting

### Common Issues

1. **No data appearing**: Check API credentials and endpoint URL
2. **Cron not running**: Verify WordPress cron is working
3. **SOAP errors**: Ensure PHP SOAP extension is installed
4. **Connection timeouts**: Check server firewall settings

### Debug Mode

Enable WordPress debug mode to see detailed error logs:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Support

For support and bug reports, please contact the development team or check the plugin documentation.

## Changelog

### Version 1.0.0
- Initial release
- SOAP API integration
- WordPress cron scheduling
- Admin interface
- Public API functions
- Shortcode support
