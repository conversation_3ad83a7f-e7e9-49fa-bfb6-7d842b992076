# AITO Reviews Plugin - System Requirements

## PHP Requirements

### **Required Extensions**

#### 1. **PHP SOAP Extension** ⚠️ **CRITICAL**
- **Purpose**: Enables communication with SOAP web services
- **Used for**: Connecting to AITO's SOAP API endpoint
- **Check**: `extension_loaded('soap')`
- **Installation**: 
  - **Ubuntu/Debian**: `sudo apt-get install php-soap`
  - **CentOS/RHEL**: `sudo yum install php-soap`
  - **Windows**: Uncomment `extension=soap` in php.ini
  - **cPanel/Shared Hosting**: Enable via PHP Extensions in control panel

#### 2. **PHP OpenSSL Extension** ⚠️ **CRITICAL**
- **Purpose**: Enables secure HTTPS connections
- **Used for**: Secure communication with AITO API over HTTPS
- **Check**: `extension_loaded('openssl')`
- **Installation**: Usually enabled by default, contact hosting provider if missing

#### 3. **PHP cURL Extension** ⚠️ **CRITICAL**
- **Purpose**: HTTP client library for reliable web requests
- **Used for**: Backup HTTP communication method
- **Check**: `extension_loaded('curl')`
- **Installation**:
  - **Ubuntu/Debian**: `sudo apt-get install php-curl`
  - **CentOS/RHEL**: `sudo yum install php-curl`

#### 4. **PHP LibXML Extension** 📋 **RECOMMENDED**
- **Purpose**: XML parsing and manipulation
- **Used for**: Processing SOAP/XML responses
- **Check**: `extension_loaded('libxml')`
- **Installation**: Usually included with PHP core

### **Optional Extensions**

#### 5. **PHP DOM Extension** 📋 **RECOMMENDED**
- **Purpose**: Advanced XML document manipulation
- **Used for**: Complex XML response parsing if needed
- **Check**: `extension_loaded('dom')`

#### 6. **PHP SimpleXML Extension** 📋 **RECOMMENDED**
- **Purpose**: Simple XML parsing
- **Used for**: Basic XML response handling
- **Check**: `extension_loaded('simplexml')`

## PHP Version Requirements

### **Minimum PHP Version: 7.0**
- **Recommended**: PHP 7.4 or higher
- **Supported**: PHP 8.0, 8.1, 8.2, 8.3
- **Reason**: Modern PHP features, security, and performance

## WordPress Requirements

### **Minimum WordPress Version: 5.0**
- **Recommended**: Latest stable version
- **Required Features**:
  - WordPress Cron system
  - Options API
  - Settings API
  - AJAX functionality

## Server Configuration

### **PHP Settings**
```ini
; Recommended PHP settings
max_execution_time = 60        ; Allow time for SOAP requests
memory_limit = 256M           ; Sufficient memory for XML processing
allow_url_fopen = On          ; Enable URL file access (if needed)
default_socket_timeout = 60   ; Socket timeout for SOAP requests
```

### **Network Requirements**
- **Outbound HTTPS**: Must allow connections to `https://www.aito.com`
- **Port 443**: HTTPS traffic must be allowed
- **Firewall**: No blocking of outbound SOAP/HTTP requests

## Hosting Compatibility

### **✅ Compatible Hosting Types**
- **VPS/Dedicated Servers**: Full control over PHP extensions
- **Managed WordPress Hosting**: Most providers support required extensions
- **Shared Hosting**: Usually compatible (check with provider)
- **Cloud Hosting**: AWS, Google Cloud, Azure typically compatible

### **⚠️ Potential Issues**
- **Cheap Shared Hosting**: May not have SOAP extension
- **Restricted Environments**: Some hosts disable SOAP for security
- **Older Hosting**: May run outdated PHP versions

## Installation Verification

### **Automatic Check**
The plugin automatically checks requirements on activation and will:
- ✅ Activate successfully if all requirements are met
- ❌ Prevent activation and show error message if requirements are missing
- 📊 Display system information in admin settings

### **Manual Check**
You can verify requirements manually:

```php
// Check PHP version
echo 'PHP Version: ' . PHP_VERSION . "\n";

// Check required extensions
$required = ['soap', 'openssl', 'curl', 'libxml'];
foreach ($required as $ext) {
    echo $ext . ': ' . (extension_loaded($ext) ? 'OK' : 'MISSING') . "\n";
}
```

### **WordPress Admin Check**
1. Go to **Settings > AITO Reviews**
2. Scroll to **System Information** section
3. Review the requirements table
4. Green checkmarks = OK, Red X = Missing

## Troubleshooting

### **Common Issues**

#### "SOAP Extension Not Found"
```
Solution:
1. Contact your hosting provider
2. Request PHP SOAP extension installation
3. Or enable it in cPanel > PHP Extensions
```

#### "SSL Certificate Verify Failed"
```
Solution:
1. Update server CA certificates
2. Or add to php.ini:
   curl.cainfo = "/path/to/cacert.pem"
```

#### "Connection Timeout"
```
Solution:
1. Increase max_execution_time in php.ini
2. Check firewall settings
3. Verify network connectivity to aito.com
```

### **Testing Connection**
Use the built-in connection test:
1. Go to **Settings > AITO Reviews**
2. Configure your API endpoint
3. Click **Test Connection**
4. Review results and error messages

## Hosting Provider Specific Notes

### **Popular Hosting Providers**

#### **SiteGround**
- ✅ SOAP extension available
- Enable via cPanel > PHP Manager

#### **Bluehost**
- ✅ SOAP extension available
- Enable via cPanel > MultiPHP Manager

#### **WP Engine**
- ✅ SOAP extension enabled by default
- Contact support if issues

#### **Kinsta**
- ✅ SOAP extension available
- Contact support to enable if needed

#### **GoDaddy**
- ⚠️ May need to request SOAP extension
- Available on higher-tier plans

## Security Considerations

### **SOAP Security**
- Always use HTTPS endpoints
- Store credentials securely
- Use WordPress nonces for admin actions
- Validate and sanitize all data

### **Network Security**
- Whitelist AITO API endpoints if using strict firewall
- Monitor outbound connections
- Use strong authentication credentials

## Performance Optimization

### **Caching**
- Plugin stores data in WordPress options (cached)
- Use object caching for better performance
- Consider CDN for static assets

### **Cron Optimization**
- Choose appropriate update intervals
- Monitor cron job performance
- Use server cron instead of WordPress cron for high-traffic sites

## Getting Help

### **If Requirements Are Missing**
1. **Contact Hosting Provider**: Most can enable required extensions
2. **Upgrade Hosting Plan**: Higher tiers often include more extensions
3. **Switch Hosting**: Consider WordPress-optimized hosting

### **For Technical Support**
- Check WordPress debug logs
- Use plugin's connection test feature
- Review system information in admin panel
- Contact plugin developer with specific error messages

## Summary Checklist

Before installing the AITO Reviews plugin, ensure:

- [ ] PHP 7.0 or higher
- [ ] PHP SOAP extension enabled
- [ ] PHP OpenSSL extension enabled  
- [ ] PHP cURL extension enabled
- [ ] Outbound HTTPS connections allowed
- [ ] WordPress 5.0 or higher
- [ ] Admin access to configure settings

If all items are checked, the plugin should work correctly on your system.
