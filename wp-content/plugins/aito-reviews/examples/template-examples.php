<?php
/**
 * AITO Reviews Template Examples
 * 
 * This file contains examples of how to use the AITO Reviews plugin
 * in your WordPress theme templates.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

?>

<!-- Example 1: Basic Review Display -->
<div class="review-section">
    <h3>Customer Reviews</h3>
    <?php if (function_exists('aito_has_review_data') && aito_has_review_data()): ?>
        <div class="review-summary">
            <span class="score"><?php aito_review_score(1); ?></span>
            <span class="total">(<?php aito_review_total(); ?> reviews)</span>
        </div>
    <?php else: ?>
        <p>No reviews available at this time.</p>
    <?php endif; ?>
</div>

<!-- Example 2: Star Rating Display -->
<div class="star-rating-section">
    <h3>Our Rating</h3>
    <?php 
    if (function_exists('aito_review_summary')) {
        aito_review_summary(array(
            'template' => 'stars',
            'show_total' => true,
            'wrapper_class' => 'custom-rating-wrapper'
        ));
    }
    ?>
</div>

<!-- Example 3: Card Style Display -->
<div class="review-card-container">
    <?php if (function_exists('aito_get_review_data')): ?>
        <?php $review_data = aito_get_review_data(); ?>
        <div class="aito-review-card">
            <div class="score-display">
                <span class="big-score"><?php echo esc_html(number_format($review_data['score'], 1)); ?></span>
                <span class="score-label">out of 10</span>
            </div>
            <div class="star-rating">
                <?php aito_review_summary(array('template' => 'stars', 'show_score' => false, 'show_total' => false)); ?>
            </div>
            <div class="review-count">
                Based on <?php echo esc_html(number_format($review_data['total'])); ?> reviews
            </div>
            <?php if ($review_data['last_updated']): ?>
                <div class="last-updated">
                    Updated <?php echo esc_html(human_time_diff($review_data['last_updated'], current_time('timestamp'))); ?> ago
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Example 4: Inline Review Display -->
<p class="product-description">
    Our tours are highly rated 
    <?php if (function_exists('aito_has_review_data') && aito_has_review_data()): ?>
        (<?php aito_review_score(1); ?>/10 from <?php aito_review_total(); ?> reviews)
    <?php endif; ?>
    and provide unforgettable experiences.
</p>

<!-- Example 5: Conditional Display with Fallback -->
<?php if (function_exists('aito_has_review_data')): ?>
    <?php if (aito_has_review_data()): ?>
        <div class="testimonial-section">
            <h4>What Our Customers Say</h4>
            <div class="rating-display">
                <?php aito_review_summary(array(
                    'show_score' => true,
                    'show_total' => true,
                    'show_last_update' => false,
                    'wrapper_class' => 'testimonial-rating'
                )); ?>
            </div>
        </div>
    <?php else: ?>
        <div class="no-reviews">
            <p>Be the first to review our services!</p>
        </div>
    <?php endif; ?>
<?php endif; ?>

<!-- Example 6: Widget-Style Display -->
<aside class="review-widget">
    <h4>Customer Satisfaction</h4>
    <?php if (function_exists('aito_get_review_score')): ?>
        <?php $score = aito_get_review_score(); ?>
        <?php if ($score > 0): ?>
            <div class="widget-content">
                <div class="score-circle">
                    <span class="score-number"><?php echo esc_html(number_format($score, 1)); ?></span>
                    <span class="score-max">/10</span>
                </div>
                <div class="review-info">
                    <div class="star-display">
                        <?php aito_review_summary(array('template' => 'stars', 'show_score' => false, 'show_total' => false)); ?>
                    </div>
                    <div class="review-count">
                        <?php aito_review_total(); ?> reviews
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</aside>

<!-- Example 7: Header/Banner Integration -->
<header class="site-header">
    <div class="header-content">
        <h1>Absolute Escapes</h1>
        <p class="tagline">Premium Travel Experiences</p>
        <?php if (function_exists('aito_has_review_data') && aito_has_review_data()): ?>
            <div class="header-rating">
                <span class="rating-text">Rated</span>
                <span class="rating-score"><?php aito_review_score(1); ?></span>
                <div class="rating-stars">
                    <?php aito_review_summary(array('template' => 'stars', 'show_score' => false, 'show_total' => false)); ?>
                </div>
                <span class="rating-count">(<?php aito_review_total(); ?> reviews)</span>
            </div>
        <?php endif; ?>
    </div>
</header>

<!-- Example 8: Footer Integration -->
<footer class="site-footer">
    <div class="footer-content">
        <div class="footer-section">
            <h5>Customer Reviews</h5>
            <?php if (function_exists('aito_has_review_data') && aito_has_review_data()): ?>
                <div class="footer-rating">
                    <?php aito_review_summary(array(
                        'show_score' => true,
                        'show_total' => true,
                        'wrapper_class' => 'footer-review-summary'
                    )); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</footer>

<!-- Example 9: Custom Styling with CSS Classes -->
<div class="custom-review-display">
    <?php 
    if (function_exists('aito_review_summary')) {
        aito_review_summary(array(
            'wrapper_class' => 'my-custom-wrapper',
            'score_class' => 'my-score-style',
            'total_class' => 'my-total-style',
            'show_last_update' => true,
            'update_class' => 'my-update-style'
        ));
    }
    ?>
</div>

<!-- Example 10: Advanced Usage with Error Handling -->
<?php
if (function_exists('aito_get_plugin_status')) {
    $status = aito_get_plugin_status();
    
    if ($status['enabled'] && $status['has_data']) {
        // Plugin is working and has data
        echo '<div class="review-status-good">';
        aito_review_summary();
        echo '</div>';
    } elseif ($status['enabled'] && !$status['has_data']) {
        // Plugin is enabled but no data yet
        echo '<div class="review-status-pending">';
        echo '<p>Review data is being updated...</p>';
        echo '</div>';
    } elseif (!empty($status['last_error'])) {
        // There's an error (only show to admins)
        if (current_user_can('manage_options')) {
            echo '<div class="review-status-error">';
            echo '<p>Review system error: ' . esc_html($status['last_error']) . '</p>';
            echo '</div>';
        }
    }
}
?>

<style>
/* Custom CSS for examples */
.review-section {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.review-summary .score {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
    margin-right: 10px;
}

.review-summary .total {
    color: #666;
}

.score-circle {
    display: inline-block;
    text-align: center;
    margin-right: 15px;
}

.score-number {
    font-size: 36px;
    font-weight: bold;
    color: #0073aa;
}

.score-max {
    font-size: 18px;
    color: #666;
}

.header-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
}

.rating-score {
    font-size: 20px;
    font-weight: bold;
    color: #ffb900;
}

.footer-rating {
    font-size: 14px;
}

.review-status-good {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    padding: 10px;
    border-radius: 4px;
}

.review-status-pending {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 10px;
    border-radius: 4px;
}

.review-status-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 4px;
}
</style>
