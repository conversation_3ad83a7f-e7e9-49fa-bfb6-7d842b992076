<?php
/**
 * AITO Reviews Plugin Uninstall Script
 * 
 * This file is executed when the plugin is uninstalled (deleted) from WordPress.
 * It cleans up all plugin data from the database.
 */

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Clear scheduled cron job
$timestamp = wp_next_scheduled('aito_reviews_fetch_data');
if ($timestamp) {
    wp_unschedule_event($timestamp, 'aito_reviews_fetch_data');
}

// List of plugin options to remove
$options_to_remove = array(
    'aito_reviews_endpoint_url',
    'aito_reviews_username',
    'aito_reviews_password',
    'aito_reviews_company_id',
    'aito_reviews_cron_interval',
    'aito_reviews_enabled',
    'aito_reviews_score',
    'aito_reviews_total',
    'aito_reviews_last_update',
    'aito_reviews_last_error',
    'aito_reviews_data',
    'aito_reviews_raw_response',
    'aito_reviews_log'
);

// Remove all plugin options
foreach ($options_to_remove as $option) {
    delete_option($option);
}

// For multisite installations, remove options from all sites
if (is_multisite()) {
    global $wpdb;
    
    // Get all blog IDs
    $blog_ids = $wpdb->get_col("SELECT blog_id FROM $wpdb->blogs");
    
    foreach ($blog_ids as $blog_id) {
        switch_to_blog($blog_id);
        
        // Clear cron job for this site
        $timestamp = wp_next_scheduled('aito_reviews_fetch_data');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'aito_reviews_fetch_data');
        }
        
        // Remove options for this site
        foreach ($options_to_remove as $option) {
            delete_option($option);
        }
        
        restore_current_blog();
    }
}

// Clear any cached data
wp_cache_flush();

// Log the uninstall (if debug is enabled)
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('AITO Reviews Plugin: Uninstalled and cleaned up all data');
}
