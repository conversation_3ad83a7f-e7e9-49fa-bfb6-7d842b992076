<?php
/**
 * AITO SOAP Client Class
 * 
 * Handles SOAP communication with AITO API
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AITO_SOAP_Client {
    
    /**
     * SOAP client instance
     */
    private $soap_client;
    
    /**
     * API endpoint URL
     */
    private $endpoint_url;
    
    /**
     * Authentication credentials
     */
    private $username;
    private $password;
    private $company_id;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->endpoint_url = get_option('aito_reviews_endpoint_url', 'https://www.aito.com/api/v1/api.asmx');
        $this->username = get_option('aito_reviews_username', '');
        $this->password = get_option('aito_reviews_password', '');
        $this->company_id = get_option('aito_reviews_company_id', '');
    }
    
    /**
     * Initialize SOAP client
     */
    private function init_soap_client() {
        if ($this->soap_client !== null) {
            return true;
        }
        
        try {
            // SOAP client options
            $options = array(
                'trace' => true,
                'exceptions' => true,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'connection_timeout' => 30,
                'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION,
                'stream_context' => stream_context_create(array(
                    'http' => array(
                        'timeout' => 30,
                        'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION
                    )
                ))
            );
            
            // Add authentication if provided
            if (!empty($this->username) && !empty($this->password)) {
                $options['login'] = $this->username;
                $options['password'] = $this->password;
            }
            
            // Create SOAP client
            $wsdl_url = $this->endpoint_url . '?WSDL';
            $this->soap_client = new SoapClient($wsdl_url, $options);
            
            return true;
            
        } catch (Exception $e) {
            $this->log_error('SOAP Client Initialization Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get feedback rating data
     */
    public function get_feedback_rating() {
        if (!$this->init_soap_client()) {
            return false;
        }
        
        try {
            // Prepare SOAP request parameters
            $params = array();
            
            // Add company ID if provided
            if (!empty($this->company_id)) {
                $params['companyId'] = $this->company_id;
            }
            
            // Call getFeedbackRatingA method
            $response = $this->soap_client->getFeedbackRatingA($params);
            
            // Process response
            return $this->process_feedback_response($response);
            
        } catch (SoapFault $e) {
            $this->log_error('SOAP Fault: ' . $e->getMessage());
            return false;
        } catch (Exception $e) {
            $this->log_error('General Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Process feedback response
     */
    private function process_feedback_response($response) {
        if (empty($response)) {
            $this->log_error('Empty response received from AITO API');
            return false;
        }
        
        // Initialize default values
        $processed_data = array(
            'score' => 0,
            'total' => 0,
            'last_updated' => current_time('timestamp'),
            'raw_response' => $response
        );
        
        try {
            // Handle different response formats
            if (is_object($response)) {
                // Convert object to array for easier processing
                $response_array = json_decode(json_encode($response), true);
            } elseif (is_string($response)) {
                // Try to parse as JSON first
                $json_response = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $response_array = $json_response;
                } else {
                    // Try to parse as XML
                    $xml_response = simplexml_load_string($response);
                    if ($xml_response !== false) {
                        $response_array = json_decode(json_encode($xml_response), true);
                    } else {
                        $response_array = array('raw' => $response);
                    }
                }
            } else {
                $response_array = (array) $response;
            }
            
            // Extract score and total from response
            $processed_data['score'] = $this->extract_score($response_array);
            $processed_data['total'] = $this->extract_total($response_array);
            
            // Log successful response
            $this->log_success('Successfully retrieved feedback rating data');
            
            return $processed_data;
            
        } catch (Exception $e) {
            $this->log_error('Error processing response: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Extract score from response
     */
    private function extract_score($response_array) {
        // Common field names for score
        $score_fields = array('score', 'rating', 'average', 'averageRating', 'feedbackScore');
        
        foreach ($score_fields as $field) {
            if (isset($response_array[$field])) {
                return floatval($response_array[$field]);
            }
        }
        
        // Try nested structures
        if (isset($response_array['result'])) {
            foreach ($score_fields as $field) {
                if (isset($response_array['result'][$field])) {
                    return floatval($response_array['result'][$field]);
                }
            }
        }
        
        if (isset($response_array['data'])) {
            foreach ($score_fields as $field) {
                if (isset($response_array['data'][$field])) {
                    return floatval($response_array['data'][$field]);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Extract total from response
     */
    private function extract_total($response_array) {
        // Common field names for total
        $total_fields = array('total', 'count', 'totalReviews', 'reviewCount', 'feedbackCount');
        
        foreach ($total_fields as $field) {
            if (isset($response_array[$field])) {
                return intval($response_array[$field]);
            }
        }
        
        // Try nested structures
        if (isset($response_array['result'])) {
            foreach ($total_fields as $field) {
                if (isset($response_array['result'][$field])) {
                    return intval($response_array['result'][$field]);
                }
            }
        }
        
        if (isset($response_array['data'])) {
            foreach ($total_fields as $field) {
                if (isset($response_array['data'][$field])) {
                    return intval($response_array['data'][$field]);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Test connection to AITO API
     */
    public function test_connection() {
        if (!$this->init_soap_client()) {
            return array(
                'success' => false,
                'message' => 'Failed to initialize SOAP client'
            );
        }
        
        try {
            // Try to get WSDL functions
            $functions = $this->soap_client->__getFunctions();
            
            if (empty($functions)) {
                return array(
                    'success' => false,
                    'message' => 'No SOAP functions available'
                );
            }
            
            // Check if getFeedbackRatingA method exists
            $has_method = false;
            foreach ($functions as $function) {
                if (strpos($function, 'getFeedbackRatingA') !== false) {
                    $has_method = true;
                    break;
                }
            }
            
            if (!$has_method) {
                return array(
                    'success' => false,
                    'message' => 'getFeedbackRatingA method not found in WSDL'
                );
            }
            
            return array(
                'success' => true,
                'message' => 'Connection successful',
                'functions' => $functions
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Log error message
     */
    private function log_error($message) {
        update_option('aito_reviews_last_error', current_time('mysql') . ': ' . $message);
        
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Plugin Error: ' . $message);
        }
    }
    
    /**
     * Log success message
     */
    private function log_success($message) {
        update_option('aito_reviews_last_error', ''); // Clear previous errors
        
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Plugin Success: ' . $message);
        }
    }
}
