<?php
/**
 * AITO SOAP Client Class
 * 
 * Handles SOAP communication with AITO API
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AITO_SOAP_Client {
    
    /**
     * SOAP client instance
     */
    private $soap_client;
    
    /**
     * API endpoint URL
     */
    private $endpoint_url;
    
    /**
     * Authentication credentials
     */
    private $username;
    private $password;
    private $company_id;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->endpoint_url = get_option('aito_reviews_endpoint_url', 'https://www.aito.com/api/v1/api.asmx');
        $this->username = get_option('aito_reviews_username', '');
        $this->password = get_option('aito_reviews_password', '');
        $this->company_id = get_option('aito_reviews_company_id', '');
    }
    
    /**
     * Initialize SOAP client
     */
    private function init_soap_client() {
        if ($this->soap_client !== null) {
            return true;
        }
        
        try {
            // SOAP client options
            $options = array(
                'trace' => true,
                'exceptions' => true,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'connection_timeout' => 30,
                'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION,
                'stream_context' => stream_context_create(array(
                    'http' => array(
                        'timeout' => 30,
                        'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION
                    )
                ))
            );

            // Note: Authentication is now handled via SOAP parameters, not HTTP auth
            
            // Create SOAP client
            $wsdl_url = $this->endpoint_url . '?WSDL';
            $this->soap_client = new SoapClient($wsdl_url, $options);
            
            return true;
            
        } catch (Exception $e) {
            $this->log_error('SOAP Client Initialization Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get feedback rating data using specified method
     */
    public function get_feedback_rating($method = null) {
        if (!$this->init_soap_client()) {
            $this->log_error('Failed to initialize SOAP client');
            return false;
        }

        // Determine which method to use
        if (!$method) {
            $method = get_option('aito_reviews_method', 'getFeedbackRatingA');
        }

        $this->log_debug('Using SOAP method: ' . $method);

        try {
            // Prepare SOAP request parameters
            $params = array();

            // Add authentication if provided
            if (!empty($this->username) || !empty($this->password)) {
                $params['auth'] = array(
                    'username' => $this->username ?: '',
                    'password' => $this->password ?: ''
                );

                // Also try alternative auth structures that AITO might expect
                $params['authentication'] = $params['auth']; // Alternative name
                $params['credentials'] = $params['auth']; // Another alternative

                // Try flat structure as well
                $params['username'] = $this->username ?: '';
                $params['password'] = $this->password ?: '';
            } else {
                // Even if no credentials provided, create empty auth structure
                // Some APIs require the auth object to exist even if empty
                $params['auth'] = array(
                    'username' => '',
                    'password' => ''
                );
            }

            // Add company ID if provided
            if (!empty($this->company_id)) {
                $params['companyId'] = $this->company_id;
            }

            // Log request details (mask sensitive data)
            $debug_params = $params;
            if (isset($debug_params['auth'])) {
                $debug_params['auth']['password'] = '[MASKED]';
            }
            $this->log_debug('SOAP Request Parameters: ' . json_encode($debug_params));
            $this->log_debug('SOAP Endpoint: ' . $this->endpoint_url);

            // Call the specified SOAP method dynamically
            if (!method_exists($this->soap_client, $method)) {
                $this->log_error('SOAP method does not exist: ' . $method);
                return false;
            }

            $response = $this->soap_client->$method($params);

            // Log raw response
            $this->log_debug('SOAP Raw Response: ' . print_r($response, true));

            // Get SOAP request/response for debugging
            if (method_exists($this->soap_client, '__getLastRequest')) {
                $this->log_debug('SOAP Last Request: ' . $this->soap_client->__getLastRequest());
            }
            if (method_exists($this->soap_client, '__getLastResponse')) {
                $this->log_debug('SOAP Last Response: ' . $this->soap_client->__getLastResponse());
            }

            // Process response
            return $this->process_feedback_response($response);

        } catch (SoapFault $e) {
            $error_details = array(
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'faultcode' => $e->faultcode ?? 'N/A',
                'faultstring' => $e->faultstring ?? 'N/A',
                'detail' => $e->detail ?? 'N/A'
            );

            // Get SOAP request/response for debugging
            if (method_exists($this->soap_client, '__getLastRequest')) {
                $error_details['last_request'] = $this->soap_client->__getLastRequest();
            }
            if (method_exists($this->soap_client, '__getLastResponse')) {
                $error_details['last_response'] = $this->soap_client->__getLastResponse();
            }

            $this->log_error('SOAP Fault: ' . json_encode($error_details));
            return false;
        } catch (Exception $e) {
            $error_details = array(
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            );

            $this->log_error('General Error: ' . json_encode($error_details));
            return false;
        }
    }
    
    /**
     * Process feedback response
     */
    private function process_feedback_response($response) {
        if (empty($response)) {
            $this->log_error('Empty response received from AITO API');
            return false;
        }
        
        // Initialize default values
        $processed_data = array(
            'score' => 0,
            'total' => 0,
            'last_updated' => current_time('timestamp'),
            'raw_response' => $response
        );
        
        try {
            // Handle different response formats
            if (is_object($response)) {
                // Convert object to array for easier processing
                $response_array = json_decode(json_encode($response), true);
            } elseif (is_string($response)) {
                // Try to parse as JSON first
                $json_response = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $response_array = $json_response;
                } else {
                    // Try to parse as XML
                    $xml_response = simplexml_load_string($response);
                    if ($xml_response !== false) {
                        $response_array = json_decode(json_encode($xml_response), true);
                    } else {
                        $response_array = array('raw' => $response);
                    }
                }
            } else {
                $response_array = (array) $response;
            }
            
            // Extract score and total from response
            $processed_data['score'] = $this->extract_score($response_array);
            $processed_data['total'] = $this->extract_total($response_array);
            
            // Log successful response
            $this->log_success('Successfully retrieved feedback rating data');
            
            return $processed_data;
            
        } catch (Exception $e) {
            $this->log_error('Error processing response: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Extract score from response
     */
    private function extract_score($response_array) {
        // Common field names for score
        $score_fields = array('score', 'rating', 'average', 'averageRating', 'feedbackScore');
        
        foreach ($score_fields as $field) {
            if (isset($response_array[$field])) {
                return floatval($response_array[$field]);
            }
        }
        
        // Try nested structures
        if (isset($response_array['result'])) {
            foreach ($score_fields as $field) {
                if (isset($response_array['result'][$field])) {
                    return floatval($response_array['result'][$field]);
                }
            }
        }
        
        if (isset($response_array['data'])) {
            foreach ($score_fields as $field) {
                if (isset($response_array['data'][$field])) {
                    return floatval($response_array['data'][$field]);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Extract total from response
     */
    private function extract_total($response_array) {
        // Common field names for total
        $total_fields = array('total', 'count', 'totalReviews', 'reviewCount', 'feedbackCount');
        
        foreach ($total_fields as $field) {
            if (isset($response_array[$field])) {
                return intval($response_array[$field]);
            }
        }
        
        // Try nested structures
        if (isset($response_array['result'])) {
            foreach ($total_fields as $field) {
                if (isset($response_array['result'][$field])) {
                    return intval($response_array['result'][$field]);
                }
            }
        }
        
        if (isset($response_array['data'])) {
            foreach ($total_fields as $field) {
                if (isset($response_array['data'][$field])) {
                    return intval($response_array['data'][$field]);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Test connection to AITO API
     */
    public function test_connection() {
        if (!$this->init_soap_client()) {
            return array(
                'success' => false,
                'message' => 'Failed to initialize SOAP client'
            );
        }
        
        try {
            // Try to get WSDL functions
            $functions = $this->soap_client->__getFunctions();
            
            if (empty($functions)) {
                return array(
                    'success' => false,
                    'message' => 'No SOAP functions available'
                );
            }
            
            // Check for available review methods
            $review_methods = array();
            $feedback_methods = array();

            foreach ($functions as $function) {
                // Look for feedback/review related methods
                if (preg_match('/feedback|review|rating/i', $function)) {
                    $feedback_methods[] = $function;
                }

                // Specifically look for getFeedbackRating methods
                if (preg_match('/getFeedbackRating/i', $function)) {
                    $review_methods[] = $function;
                }
            }

            $this->log_debug('Available feedback methods: ' . json_encode($feedback_methods));
            $this->log_debug('Available review methods: ' . json_encode($review_methods));
            
            return array(
                'success' => true,
                'message' => 'Connection successful',
                'functions' => $functions,
                'feedback_methods' => $feedback_methods,
                'review_methods' => $review_methods,
                'total_methods' => count($functions),
                'feedback_count' => count($feedback_methods),
                'review_count' => count($review_methods)
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Log error message
     */
    private function log_error($message) {
        update_option('aito_reviews_last_error', current_time('mysql') . ': ' . $message);
        
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Plugin Error: ' . $message);
        }
    }
    
    /**
     * Log success message
     */
    private function log_success($message) {
        update_option('aito_reviews_last_error', ''); // Clear previous errors

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Plugin Success: ' . $message);
        }
    }

    /**
     * Log debug message
     */
    private function log_debug($message) {
        // Store debug messages in a separate option for admin viewing
        $debug_log = get_option('aito_reviews_debug_log', array());
        $debug_log[] = current_time('mysql') . ': ' . $message;

        // Keep only last 20 debug entries
        if (count($debug_log) > 20) {
            $debug_log = array_slice($debug_log, -20);
        }

        update_option('aito_reviews_debug_log', $debug_log);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Plugin Debug: ' . $message);
        }
    }

    /**
     * Get debug log
     */
    public function get_debug_log() {
        return get_option('aito_reviews_debug_log', array());
    }

    /**
     * Clear debug log
     */
    public function clear_debug_log() {
        return delete_option('aito_reviews_debug_log');
    }
}
