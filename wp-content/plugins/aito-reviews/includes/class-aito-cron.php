<?php
/**
 * AITO Reviews Cron Class
 * 
 * Handles scheduled data fetching
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AITO_Reviews_Cron {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Hook into the cron action
        add_action('aito_reviews_fetch_data', array($this, 'fetch_data'));
        
        // Hook into settings update to reschedule cron
        add_action('update_option_aito_reviews_cron_interval', array($this, 'reschedule_on_interval_change'), 10, 2);
        add_action('update_option_aito_reviews_enabled', array($this, 'reschedule_on_enabled_change'), 10, 2);
    }
    
    /**
     * Fetch data from AITO API
     */
    public function fetch_data() {
        // Check if plugin is enabled
        if (!get_option('aito_reviews_enabled', false)) {
            $this->log_message('Plugin is disabled, skipping data fetch');
            return false;
        }
        
        // Initialize SOAP client
        $soap_client = new AITO_SOAP_Client();
        
        // Fetch data
        $data = $soap_client->get_feedback_rating();
        
        if ($data === false) {
            $this->log_message('Failed to fetch data from AITO API');
            return false;
        }
        
        // Store data in WordPress options
        $this->store_data($data);
        
        $this->log_message('Successfully fetched and stored data');
        return true;
    }
    
    /**
     * Store fetched data
     */
    private function store_data($data) {
        if (!is_array($data)) {
            return false;
        }
        
        // Update individual options
        update_option('aito_reviews_score', $data['score']);
        update_option('aito_reviews_total', $data['total']);
        update_option('aito_reviews_last_update', $data['last_updated']);
        
        // Store raw response for debugging
        if (isset($data['raw_response'])) {
            update_option('aito_reviews_raw_response', $data['raw_response']);
        }
        
        // Store complete data array
        update_option('aito_reviews_data', $data);
        
        // Clear any previous errors
        update_option('aito_reviews_last_error', '');
        
        return true;
    }
    
    /**
     * Reschedule cron when interval changes
     */
    public function reschedule_on_interval_change($old_value, $new_value) {
        if ($old_value !== $new_value) {
            $plugin = AITO_Reviews_Plugin::get_instance();
            $plugin->reschedule_cron($new_value);
            $this->log_message('Cron rescheduled with new interval: ' . $new_value);
        }
    }
    
    /**
     * Reschedule cron when enabled status changes
     */
    public function reschedule_on_enabled_change($old_value, $new_value) {
        $plugin = AITO_Reviews_Plugin::get_instance();
        
        if ($new_value && !$old_value) {
            // Plugin was enabled
            $interval = get_option('aito_reviews_cron_interval', 'hourly');
            $plugin->reschedule_cron($interval);
            $this->log_message('Cron enabled and scheduled');
        } elseif (!$new_value && $old_value) {
            // Plugin was disabled
            $plugin->reschedule_cron('');
            $this->log_message('Cron disabled and unscheduled');
        }
    }
    
    /**
     * Get next scheduled run time
     */
    public function get_next_run() {
        $timestamp = wp_next_scheduled('aito_reviews_fetch_data');
        
        if ($timestamp) {
            return array(
                'timestamp' => $timestamp,
                'formatted' => date('Y-m-d H:i:s', $timestamp),
                'human' => human_time_diff($timestamp, current_time('timestamp'))
            );
        }
        
        return false;
    }
    
    /**
     * Get cron status
     */
    public function get_cron_status() {
        $enabled = get_option('aito_reviews_enabled', false);
        $interval = get_option('aito_reviews_cron_interval', 'hourly');
        $next_run = $this->get_next_run();
        
        return array(
            'enabled' => $enabled,
            'interval' => $interval,
            'next_run' => $next_run,
            'is_scheduled' => wp_next_scheduled('aito_reviews_fetch_data') !== false
        );
    }
    
    /**
     * Manual trigger for testing
     */
    public function trigger_manual_fetch() {
        if (!current_user_can('manage_options')) {
            return false;
        }
        
        return $this->fetch_data();
    }
    
    /**
     * Log message
     */
    private function log_message($message) {
        $log_entry = current_time('mysql') . ': ' . $message;
        
        // Store in option for admin viewing
        $log = get_option('aito_reviews_log', array());
        $log[] = $log_entry;
        
        // Keep only last 50 entries
        if (count($log) > 50) {
            $log = array_slice($log, -50);
        }
        
        update_option('aito_reviews_log', $log);
        
        // Also log to WordPress debug log if enabled
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Cron: ' . $message);
        }
    }
    
    /**
     * Get log entries
     */
    public function get_log() {
        return get_option('aito_reviews_log', array());
    }
    
    /**
     * Clear log
     */
    public function clear_log() {
        return delete_option('aito_reviews_log');
    }
    
    /**
     * Force reschedule cron
     */
    public function force_reschedule() {
        // Clear existing schedule
        $timestamp = wp_next_scheduled('aito_reviews_fetch_data');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'aito_reviews_fetch_data');
        }
        
        // Reschedule if enabled
        if (get_option('aito_reviews_enabled', false)) {
            $interval = get_option('aito_reviews_cron_interval', 'hourly');
            wp_schedule_event(time(), $interval, 'aito_reviews_fetch_data');
            $this->log_message('Cron force rescheduled with interval: ' . $interval);
            return true;
        }
        
        $this->log_message('Cron unscheduled (plugin disabled)');
        return false;
    }
}
